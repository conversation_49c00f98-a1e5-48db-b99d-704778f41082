import ListSlider from "@/components/home/<USER>";
import { PAGE_SIZE, RewardType, StatusDelivery, VoucherType } from "@/constants/Const";
import { Router } from "@/constants/Route";
import { useCart } from "@/hooks/useCart";
import { useAlert } from "@/redux/slices/alert/useAlert";
import { getUser } from "@/redux/slices/authen/authSlice";
import {
  collectVoucherPoint,
  getListVoucherByShop,
  getListVoucherByShopPublic,
  VoucherDto,
} from "@/redux/slices/voucher/voucherSlice";
import { AppDispatch, RootState } from "@/redux/store";
import { showToast } from "@/utils/common";
import { useNavigate } from "@/utils/component-util";
import { Box, CircularProgress, Stack, Typography, useTheme } from "@mui/material";
import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import ArrowRightIcon from "../icon/ArrowRightIcon";
import VoucherItem, { VoucherItemCategory } from "./VourcherItem";
import VoucherSkeleton from "./VoucherSkeleton";

interface VoucherListSlideProps {
  tabIndex?: number;
  config?: {
    type: string;
    show: boolean;
    style?: {
      title?: string;
    };
  };
  limit?: number;
}

const VoucherListSlide: React.FC<VoucherListSlideProps> = ({ tabIndex = 0, config, limit }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { cartPayment, setCartAndSave } = useCart();
  const { showAlert } = useAlert();
  const navigate = useNavigate();
  const theme = useTheme();

  const [vouchers, setVouchers] = useState<VoucherDto[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [pageIndex, setPageIndex] = useState(0);

  const observerTarget = useRef(null);

  const fetchVouchers = async (reset = false) => {
    setIsLoading(true);
    let currentPage = reset ? 0 : pageIndex;
    const params = {
      PageSize: PAGE_SIZE,
      PageIndex: currentPage,
      shopId,
      Search: "",
    };
    const response = user
      ? await dispatch(getListVoucherByShop(params))
      : await dispatch(getListVoucherByShopPublic(params));
    if (response?.payload) {
      const newVouchers = response?.payload?.result || [];
      setVouchers(
        reset
          ? response.payload?.result?.result || response.payload?.result || []
          : [
              ...(reset ? [] : vouchers),
              ...(response.payload?.result?.result || response.payload?.result || []),
            ]
      );
      setHasMore((reset ? 0 : vouchers.length) + newVouchers.length < response?.payload?.total);
      setPageIndex(currentPage + 1);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchVouchers(true);
    // eslint-disable-next-line
  }, [shopId, user]);

  const handleSelectVoucher = async (voucher) => {
    if (voucher.voucherType == VoucherType.Custom && voucher.rewardType == RewardType.Point) {
      await collectVoucher(voucher.voucherId);
      return;
    }
    try {
      if (cartPayment.listItems.length > 0) {
        if (voucher.voucherType === VoucherType.Transport) {
          if (cartPayment.statusDelivery === StatusDelivery.InHome) {
            await setCartAndSave({
              ...cartPayment,
              voucherTransport: [voucher],
            });
          } else {
            await setCartAndSave({
              ...cartPayment,
              statusDelivery: StatusDelivery.InHome,
              voucherTransport: [voucher],
            });
          }
        } else {
          await setCartAndSave({
            ...cartPayment,
            voucherPromotion: [voucher],
          });
        }
        navigate(Router.cartPayment);
      } else {
        showToast({
          content: "Vui lòng chọn sản phẩm trước khi áp dụng mã giảm giá",
          type: "error",
        });
      }
    } catch (error) {
      if ((error as any).status === 400) {
        navigate(Router.menu);
      }
    }
  };

  const collectVoucher = async (voucherId) => {
    const res = await dispatch(collectVoucherPoint(voucherId));
    if (res?.payload?.success) {
      fetchVouchers(true);
      await dispatch(getUser());
      showAlert({
        title: "Chúc mừng",
        content: res?.payload?.message,
        buttons: [
          {
            title: "Đóng",
          },
        ],
      });
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading) {
          fetchVouchers(false);
        }
      },
      { threshold: 0.1 }
    );
    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }
    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [hasMore, isLoading]);

  const title = config?.style?.title || "Ưu đãi";

  if (isLoading && !vouchers.length) {
    return (
      <Box sx={{ background: "#ffffff00", borderRadius: 3, my: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1} px={2}>
          <Typography
            variant="h6"
            fontWeight={600}
            fontSize={16}
            sx={{ color: theme.palette.primary.main }}
          >
            {title}
          </Typography>
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="flex-end"
            sx={{ cursor: "pointer", opacity: 0.5 }}
          >
            <Typography fontSize={14} sx={{ fontWeight: 500, color: theme.palette.primary.main }}>
              Xem thêm
            </Typography>
            <Box
              ml={0.5}
              sx={{
                "& svg": { width: 12, height: 12 },
              }}
            >
              <ArrowRightIcon fillColor={theme.palette.primary.main} />
            </Box>
          </Stack>
        </Stack>

        {/* Use VoucherSkeleton for slide layout */}
        <VoucherSkeleton count={limit || 3} />
      </Box>
    );
  }

  if (!vouchers.length) {
    return (
      <Box sx={{ background: "#ffffff00", borderRadius: 3, my: 2, px: 2 }}>
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
          <Typography
            variant="h6"
            fontWeight={600}
            fontSize={18}
            sx={{ color: theme.palette.primary.main }}
          >
            {title}
          </Typography>
        </Stack>
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            py: 4,
            px: 2,
            bgcolor: "rgba(255, 255, 255, 0.8)",
            borderRadius: 2,
            border: "2px dashed",
            borderColor: "grey.300",
          }}
        >
          <img
            src="/icons/voucher.png"
            alt="No vouchers"
            style={{ width: 48, height: 48, opacity: 0.5, marginBottom: 16 }}
          />
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Chưa có ưu đãi nào
          </Typography>
          <Typography variant="caption" color="text.secondary" textAlign="center">
            Hãy quay lại sau để xem các ưu đãi mới
          </Typography>
        </Box>
      </Box>
    );
  }

  const voucherSlider = (
    <Box
      sx={{
        width: "100%",
        "& .slick-slide": {
          height: "140px", // Ensure all slides have the same height
          "& > div": {
            height: "100%",
          },
        },
        "& .slick-track": {
          display: "flex",
          alignItems: "stretch", // Stretch all items to same height
        },
      }}
    >
      <ListSlider
        title={undefined}
        className="voucher-slider"
        sliceConfig={{
          dots: false,
          infinite: false,
          slidesToShow: 1.2,
          autoplay: false,
          arrows: false,
        }}
      >
        {vouchers.slice(0, limit).map((voucher) => (
          <VoucherItem
            item={voucher}
            category={tabIndex === 0 ? VoucherItemCategory.LIST : VoucherItemCategory.MYLIST}
            onNavigateToDetail={() => {
              const voucherCode = voucher?.voucherDetails?.[0]?.voucherCode?.toString() ?? "";
              navigate(`${Router.voucher.detail.replace(":code", voucherCode)}`, {
                state: { voucher },
              });
            }}
            isDisabled={false}
            onSelectVoucher={() => handleSelectVoucher(voucher)}
            fetchVouchers={fetchVouchers}
          />
        ))}

        {/* {hasMore && (
          <div ref={observerTarget} style={{ height: "20px" }}>
            {isLoading && (
              <Box sx={{ display: "flex", justifyContent: "center", py: 2 }}>
                <CircularProgress size={24} />
              </Box>
            )}
          </div>
        )} */}
      </ListSlider>
    </Box>
  );

  return (
    <Box sx={{ background: "#ffffff00", borderRadius: 3, my: 2 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1} px={2}>
        <Typography
          variant="h6"
          fontWeight={600}
          fontSize={16}
          sx={{ color: theme.palette.primary.main }}
        >
          {title}
        </Typography>
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="flex-end"
          sx={{ cursor: "pointer" }}
          onClick={() => navigate(Router.voucher.index + "?tab=1")}
        >
          <Typography fontSize={14} sx={{ fontWeight: 500, color: theme.palette.primary.main }}>
            Xem thêm
          </Typography>
          <Box
            ml={0.5}
            sx={{
              "& svg": { width: 12, height: 12 },
            }}
          >
            <ArrowRightIcon fillColor={theme.palette.primary.main} />
          </Box>
        </Stack>
      </Stack>
      {voucherSlider}
    </Box>
  );
};

export default VoucherListSlide;
