import React, { useMemo } from "react";
import { useAlert } from "../../redux/slices/alert/useAlert";
import { Icon } from "../../constants/Assets";
import { showToast } from "../../utils/common";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import { <PERSON>ton, Container, Stack, Typography } from "@mui/material";
import cssStyles from "../../css/styles.module.css";
import { formatPrice } from "../../utils/formatPrice";
import { COLOR, COLORS, commonStyle } from "../../constants/themes";
import { RootState } from "../../redux/store";
import { useSelector } from "react-redux";
import CheckIcon from "../icon/CheckIcon";
import { useConfigApp } from "@/hooks/useConfigApp";
import { useCart } from "@/hooks/useCart";
import LoginPopup from "@/components/LoginPopup";

export default function BottomVerify({ cart, currentAddress, handlerCreateOrder, isCreated<PERSON>rder }) {
  const { checkLogin, openLoginPopup, setOpenLoginPopup, onClickRegister, loading } =
    useCheckLogin();
  const appConfig = useConfigApp();
  const { cartPayment } = useCart();
  const { showAlert } = useAlert();
  const totalProduct = useMemo(() => {
    if (!Array.isArray(cart?.items) || !cart?.items.length) return 0;
    return cart?.items.reduce((acc, item) => acc + item.quantity, 0);
  }, [cart]);
  const { listPaymentMethod } = useSelector((state: RootState) => state.paymentMethod);

  const onClickOrderButton = () => {
    checkLogin(showConfirmOrderAlert);
  };

  const showConfirmOrderAlert = () => {
    showAlert({
      icon: (
        <CheckIcon
          primaryColor={appConfig.color.primary}
          secondaryColor={appConfig.bgColor.primary}
        />
      ),
      title: "Xác nhận đặt hàng",
      content: "Bạn có chắc muốn đặt hàng không?",
      contentStyle: {
        color: "#1D1D1D",
      },
      buttons: [
        {
          title: "Huỷ",
        },
        {
          title: "Xác nhận",
          action: handleConfirmOrder,
        },
      ],
    });
  };

  const handleConfirmOrder = () => {
    const isExpress = cart.statusDelivery === "ExpressDelivery";
    const noAddress = !currentAddress;

    if (isExpress && noAddress) {
      showToast({
        content: "Bạn chưa có thông tin địa chỉ",
        type: "error",
      });
      return;
    }

    handlerCreateOrder();
  };

  return (
    <Container className={cssStyles.bottomPayElement}>
      <LoginPopup
        open={openLoginPopup}
        onClose={() => setOpenLoginPopup(false)}
        onClickRegister={onClickRegister}
        loading={loading}
      />
      <Stack sx={styles.bottomContainer} direction="row" gap={1}>
        <Stack width={"100%"}>
          <Stack direction={"row"} justifyContent={"end"} width={"100%"} alignItems={"baseline"}>
            <Typography style={styles.titleStyle}>Tổng tiền</Typography>
            <Typography style={{ ...styles.textStyle, color: appConfig.color.primary }}>
              {formatPrice(cartPayment?.totalAfterTax)}
            </Typography>
          </Stack>
          <Stack direction={"row"} justifyContent={"end"} width={"100%"} alignItems={"baseline"}>
            <Typography style={styles.titleStyle}>Tiết kiệm</Typography>
            <Typography
              style={{
                ...styles.textSmallStyle,
                color: appConfig.color.primary,
              }}
            >
              {formatPrice(cartPayment?.savingMoney)}
            </Typography>
          </Stack>
        </Stack>
        <Button
          style={{
            ...styles.orderBtn,
            background: appConfig.color.primary,
            color: COLOR.text.white,
          }}
          sx={{
            opacity: !isCreatedOrder ? 0.5 : 1,
            pointerEvents: !isCreatedOrder ? "none" : "auto",
            cursor: !isCreatedOrder ? "not-allowed" : "pointer",
          }}
          onClick={onClickOrderButton}
        >
          Đặt hàng
        </Button>
      </Stack>
    </Container>
  );
}

const styles: Record<string, React.CSSProperties> = {
  sectionContainer: {
    borderRadius: 4,
    background: "#fff",
    padding: 2,
    marginBottom: 1,
  },
  bottomContainer: {
    minWidth: "100%",
    alignItems: "center",
    justifyContent: "space-between",
  },
  orderBtn: {
    background: COLORS.primary,
    color: "#fff",
    paddingBlock: 10,
    borderRadius: 5,
    fontSize: 17,
    minWidth: 130,
  },
  titleStyle: {
    color: "#919191",
    fontSize: 13,
    fontWeight: 400,
    marginRight: 3,
    whiteSpace: "nowrap",
  },
  textStyle: {
    fontSize: 19,
    fontWeight: 700,
    whiteSpace: "nowrap",
  },
  textSmallStyle: {
    fontSize: 13,
    fontWeight: 400,
  },
};
