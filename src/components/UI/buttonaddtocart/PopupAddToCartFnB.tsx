import { getMinPriceInListVariant } from "@/components/products/item/ProductItemFnB";
import ItemOptionSelector from "@/components/products/ItemOptionSelector";
import VariantSelector from "@/components/products/VariantSelector";
import { formatProductItemOption, getListItemOptionUser } from "@/components/UI/PopupIconAddToCart";
import { Icon } from "@/constants/Assets";
import { SomethingWrong } from "@/constants/Const";
import { COLORS } from "@/constants/themes";
import { useCart } from "@/hooks/useCart";
import { useCheckLogin } from "@/hooks/useCheckLogin";
import { useConfigApp } from "@/hooks/useConfigApp";
import { IProduct, IProductVariant } from "@/types/product";
import { showToast } from "@/utils/common";
import { useNavigate } from "@/utils/component-util";
import { formatPrice } from "@/utils/formatPrice";
import AddIcon from "@mui/icons-material/Add";
import HorizontalRuleIcon from "@mui/icons-material/HorizontalRule";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  IconButton,
  Slide,
  Stack,
  TextField,
  Typography,
} from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { TransitionProps } from "@mui/material/transitions";
import React, { useEffect, useState } from "react";

const Transition = React.forwardRef(function Transition(
  props: TransitionProps & {
    children: React.ReactElement<any, any>;
  },
  ref: React.Ref<unknown>
) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const PopupAddToCart: React.FC<{
  product: IProduct;
  isOpen: boolean;
  onOpen: () => void;
  onClose: () => void;
  navigateAfterAdd?: () => void;
  shouldNavigate?: boolean;
}> = ({ product, isOpen, onOpen, onClose, navigateAfterAdd, shouldNavigate }) => {
  const nav = useNavigate();
  const { addProductToCart } = useCart();
  const theme = useTheme();
  const { color } = useConfigApp();
  const [quantity, setQuantity] = useState(1);
  const [note, setNote] = useState("");
  const [loading, setLoading] = useState<boolean>(false);
  const [isDisabled, setIsDisabled] = useState<boolean>(false);
  const { listVariant } = product;
  const minPriceIndex = product?.listVariant.reduce((minIndex, current, currentIndex, array) => {
    if (current.quantity <= 0) return minIndex;

    if (array[minIndex].quantity <= 0) return currentIndex;

    return current.price < array[minIndex].price ? currentIndex : minIndex;
  }, 0);
  const [selectedVariant, setSelectedVariant] = useState<IProductVariant | null>(
    product?.listVariant[minPriceIndex]
  );
  const appConfig = useConfigApp();
  const [imageSrc, setImageSrc] = useState(
    selectedVariant?.variantImage?.link || product.images?.[0]?.link
  );
  useEffect(() => {
    setImageSrc(selectedVariant?.variantImage?.link || product.images?.[0]?.link);
  }, [selectedVariant, product.images]);
  const { checkLogin } = useCheckLogin();
  const [selectedItemOptions, setSelectedItemOptions] = useState<any[]>([]);
  const [productItemOptions, setProductItemOptions] = useState<any[]>([]);
  const reduce = () => {
    setQuantity((quantity) => (quantity > 0 ? quantity - 1 : quantity));
  };
  const increase = () => {
    setQuantity(quantity + 1);
  };

  const listProductItemOptions = async (itemOptionGroupIds) => {
    const response = await getListItemOptionUser(itemOptionGroupIds);

    if (response?.data) {
      const productItemOption = response.data.data;
      const data = formatProductItemOption(productItemOption, product?.extraItemOptionGroups ?? []);
      setProductItemOptions(data);
    }
  };

  const handleVariantChange = (updatedVariant: IProductVariant) => {
    const foundVariant = product.listVariant.find(
      (variant) =>
        variant.variantNameOne === updatedVariant.variantNameOne &&
        variant.variantValueOne === updatedVariant.variantValueOne &&
        variant.variantNameTwo === updatedVariant.variantNameTwo &&
        variant.variantValueTwo === updatedVariant.variantValueTwo &&
        variant.variantNameThree === updatedVariant.variantNameThree &&
        variant.variantValueThree === updatedVariant.variantValueThree
    );

    if (foundVariant) {
      setSelectedVariant(foundVariant);
      setIsDisabled(false);
      return;
    } else {
      setSelectedVariant(null);
      setIsDisabled(true);
    }
  };

  const handleAddToCart = async () => {
    // checkLogin(acceptAddToCart);
    await acceptAddToCart();
  };

  const acceptAddToCart = async () => {
    if (product.isVariant && !selectedVariant) {
      showToast({
        content: "Vui lòng chọn thuộc tính sản phẩm",
        type: "error",
      });
      return;
    }
    if (productItemOptions && productItemOptions?.length > 0) {
      const requiredItemOptionGroups = productItemOptions.filter((item) => item.require);
      let isValidItemOptionSelected = true;
      for (const requiredItemOption of requiredItemOptionGroups) {
        const requiredItemIds = requiredItemOption.itemOptions.map((i) => i.itemOptionId);
        const hasMatch = selectedItemOptions.some((option) =>
          requiredItemIds.includes(option.itemOptionId)
        );

        if (!hasMatch) {
          setLoading(false);
          const shortName =
            requiredItemOption.name.length > 20
              ? requiredItemOption.name.slice(0, 100) + "..."
              : requiredItemOption.name;
          showToast({
            content: `Vui lòng chọn ${shortName}`,
            type: "error",
          });
          isValidItemOptionSelected = false;
          break; // Thoát khỏi vòng lặp ngay khi có lỗi
        }
      }
      if (!isValidItemOptionSelected) return; // Nếu có lỗi, dừng hàm luôn
    }

    const productWithSelectedVariant: IProduct = {
      ...product,
      variantNameOne: selectedVariant?.variantNameOne || product.listVariant[0].variantNameOne,
      variantValueOne: selectedVariant?.variantValueOne || product.listVariant[0].variantValueOne,
      variantNameTwo: selectedVariant?.variantNameTwo || product.listVariant[0].variantNameTwo,
      variantValueTwo: selectedVariant?.variantValueTwo || product.listVariant[0].variantValueTwo,
      variantNameThree:
        selectedVariant?.variantNameThree || product.listVariant[0].variantNameThree,
      variantValueThree:
        selectedVariant?.variantValueThree || product.listVariant[0].variantValueThree,
      price: selectedVariant?.price || product.listVariant[0].price,
      priceReal: selectedVariant?.priceReal || product.listVariant[0].priceReal,
      priceCapital: selectedVariant?.priceCapital || product.listVariant[0].priceCapital,
      itemsId: selectedVariant?.itemsId || product.listVariant[0].itemsId,
    };

    if (selectedItemOptions.length > 0) {
      productWithSelectedVariant.extraOptions = selectedItemOptions.map(
        (option) => option.itemOptionId
      );
      const extraPrice = selectedItemOptions.reduce((sum, item) => sum + (item.price || 0), 0);

      productWithSelectedVariant.price += extraPrice;
    }
    productWithSelectedVariant.note = note;
    setLoading(true);
    const result = await addProductToCart(productWithSelectedVariant, quantity);
    if (result) {
      showToast({
        content: "Thêm vào giỏ thành công",
        type: "success",
      });
      setLoading(false);
      setSelectedItemOptions([]);
      setQuantity(1);
      onClose();
      shouldNavigate && navigateAfterAdd && navigateAfterAdd();
    } else {
      showToast({
        content: SomethingWrong,
        type: "error",
      });
    }
  };

  const quantityRemain = (() => {
    if (!product.listVariant || product.listVariant.length === 0) return 0;

    const availableVariants = product.listVariant.filter((variant) => variant.quantity > 0);

    if (availableVariants.length === 0) return 0;

    if (product.isVariant && selectedVariant) {
      return (
        product.listVariant.find((variant) => variant.itemsId === selectedVariant.itemsId)
          ?.quantity || 0
      );
    }

    const minPriceVariant = availableVariants.reduce(
      (min, current) => (current.price < min.price ? current : min),
      availableVariants[0]
    );

    return minPriceVariant.quantity;
  })();

  const extraOptionPrice = selectedItemOptions.reduce((acc, item) => acc + item.price, 0);

  // const salePrice =
  //   (product.isVariant && selectedVariant ? selectedVariant.price : product.listVariant[0].price) +
  //   extraOptionPrice;
  const salePrice = selectedVariant?.price || getMinPriceInListVariant(product?.listVariant) || 0;

  const priceReal =
    product.isVariant && selectedVariant
      ? selectedVariant.priceReal
      : product.listVariant[0].priceReal;

  useEffect(() => {
    if (Array.isArray(product?.extraItemOptionGroups) && product.extraItemOptionGroups.length > 0) {
      listProductItemOptions(product.extraItemOptionGroups?.map((i) => i.itemOptionGroupId));
    }
  }, [product]);

  const handleOpen = () => {
    setNote("");
    setSelectedItemOptions([]);
    onOpen();
  };

  return (
    <>
      <Box
        flex={1}
        height="100%"
        display="flex"
        alignItems="center"
        justifyContent="center"
        textAlign={"center"}
        bgcolor="#fff"
        color={color.primary}
        border="3px solid"
        borderColor={color.primary}
        onClick={handleOpen}
        sx={{
          borderTopLeftRadius: "5px",
          borderBottomLeftRadius: "5px",
          padding: 0.5,
          cursor: "pointer",
        }}
      >
        Thêm vào giỏ hàng
      </Box>

      <Dialog
        className="popup-add-to-cart"
        open={isOpen}
        TransitionComponent={Transition}
        keepMounted
        fullScreen
        onClose={(e) => {
          setNote("");
          setSelectedItemOptions([]);
          onClose();
          setLoading(false);
        }}
        aria-describedby="alert-dialog-slide-description"
        scroll="paper"
      >
        <DialogContent
          style={{
            ...styles.dialogContainer,
            maxHeight: "80vh",
            overflowY: "auto",
          }}
        >
          <Box
            style={styles.iconCloseStyle}
            onClick={(e) => {
              setNote("");
              setSelectedItemOptions([]);
              onClose();
              setLoading(false);
            }}
          >
            <img src={Icon.close} alt="Đóng" />
          </Box>
          <Box sx={styles.topPopupContainer}>
            <Box sx={styles.btnHandle}></Box>
          </Box>
          <Stack
            direction="row"
            alignItems={"end"}
            gap={2}
            paddingBlock={1}
            style={{ fontSize: 16 }}
          >
            <img
              style={styles.imageStyle}
              src={imageSrc}
              alt={product.itemsName || ""}
              onError={() => setImageSrc(appConfig?.shopLogo?.link || "")}
            />
            <Stack>
              <Typography style={styles.nameText}>{product.itemsName}</Typography>
              <Box style={styles.priceContainer}>
                <Typography
                  style={{
                    ...styles.salePriceText,
                    color: color.primary,
                  }}
                >
                  {formatPrice(salePrice)}
                </Typography>
                {priceReal !== salePrice && (
                  <Typography style={styles.discountPrice}>{formatPrice(priceReal)}</Typography>
                )}
              </Box>
              <Typography style={styles.stockText}>Kho: {quantityRemain}</Typography>
            </Stack>
          </Stack>
          <Divider />
          {/* Variants */}
          {product.isVariant && listVariant && listVariant.length > 0 && (
            <>
              <VariantSelector
                productVariants={listVariant}
                onVariantChange={handleVariantChange}
                open={isOpen}
              />
              <Divider />
            </>
          )}

          {Array.isArray(productItemOptions) && productItemOptions.length > 0 && (
            <ItemOptionSelector
              productItemOptions={productItemOptions}
              onItemOptionChange={setSelectedItemOptions}
              open={isOpen}
              setLoading={setLoading}
            />
          )}
          <Stack>
            <Typography style={styles.quantityText}>Ghi chú</Typography>
            <Box>
              <TextField
                placeholder="Nhập ghi chú"
                variant="outlined"
                multiline
                rows={1} // Số dòng hiển thị
                fullWidth
                value={note} // Giá trị của input
                onChange={(e) => setNote(e.target.value)} // Cập nhật state khi nhập
                sx={{
                  "& .MuiInputBase-root": {
                    padding: 1, // Điều chỉnh padding tổng thể
                  },
                }}
                inputProps={{ maxLength: 50 }} // Giới hạn ký tự tối đa
              />
            </Box>
          </Stack>
          <Stack direction="row" style={styles.qualityContainer}>
            <Typography style={styles.quantityText}>Số lượng</Typography>
            <Box style={styles.quantityContainer}>
              <IconButton
                style={{
                  ...styles.reduceBtn,
                  borderColor: color.primary,
                  background: "transparent",
                }}
                aria-label="fingerprint"
                color="secondary"
                onClick={reduce}
              >
                <HorizontalRuleIcon style={{ color: color.primary }} />
              </IconButton>
              <Stack direction="row" alignItems={"center"} gap={2}>
                <input
                  min={0}
                  inputMode="numeric"
                  type="number"
                  style={{
                    ...styles.qualityInput,
                    borderColor: color.primary,
                    color: color.primary,
                    background: "transparent",
                  }}
                  onChange={(e) => {
                    setQuantity(Number(e.target.value));
                  }}
                  value={quantity}
                />
              </Stack>
              <IconButton
                style={{
                  ...styles.increaseBtn,
                  borderColor: color.primary,
                  background: "transparent",
                }}
                aria-label="fingerprint"
                color="secondary"
                onClick={increase}
              >
                <AddIcon style={{ color: color.primary }} />
              </IconButton>
            </Box>
          </Stack>
        </DialogContent>
        <DialogActions sx={styles.bottomBtnContainer}>
          <Button
            style={{
              ...styles.bottomBtn,
              background:
                quantityRemain <= 0 || loading || isDisabled || quantity === 0
                  ? "#B8B8B8"
                  : color.primary,
              color: COLORS.white,
            }}
            onClick={handleAddToCart}
            disabled={quantityRemain <= 0 || loading || isDisabled || quantity === 0}
          >
            {loading ? "Đang xử lý..." : "Thêm vào giỏ hàng"}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default PopupAddToCart;

const styles: Record<string, React.CSSProperties> = {
  dialogContainer: {
    paddingInline: 16,
    paddingBottom: 0,
  },
  iconCloseStyle: {
    top: 0,
    position: "absolute",
    right: 0,
    padding: 10,
  },
  imageStyle: {
    width: 130,
    height: 130,
    borderRadius: 5,
    aspectRatio: 1 / 1,
    objectFit: "cover",
  },
  topPopupContainer: {
    position: "absolute",
    top: 10,
    left: 0,
    right: 0,
    width: "100%",
    display: "flex",
    justifyContent: "center",
  },
  btnHandle: {
    width: "60px",
    height: "5px",
    background: "#DCDCDC",
    borderRadius: 99,
  },
  nameText: {
    color: "#444",
    fontSize: 14,
    fontWeight: 500,
    lineHeight: "20px",
    marginBottom: 5,
    display: "-webkit-box",
    WebkitLineClamp: 2,
    WebkitBoxOrient: "vertical",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  priceContainer: {
    display: "block",
    alignItems: "baseline",
    gap: 4,
  },
  salePriceText: {
    fontSize: 19,
    fontWeight: 500,
  },
  discountPrice: {
    textDecoration: "line-through",
    fontSize: 14,
    fontWeight: 400,
    color: "#B8B8B8",
  },
  stockText: {
    color: "#BBB",
    fontSize: 14,
    fontWeight: 500,
  },
  qualityContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 10,
  },
  quantityText: {
    color: "#878787",
    fontWeight: 500,
    fontSize: 14,
  },
  reduceBtn: {
    borderRadius: "2px 0px 0px 2px",
    border: "1px solid",
    background: "#bed7fa",
    width: 30,
    height: 30,
  },
  increaseBtn: {
    borderRadius: "0px 2px 2px 0px",
    border: "1px solid",
    background: "#bed7fa",
    width: 30,
    height: 30,
  },
  qualityInput: {
    width: 70,
    height: 30,
    border: "1px solid #0368F7",
    textAlign: "center",
    fontWeight: 700,
    borderInline: "none",
    borderRadius: 0,
    fontSize: 20,
  },
  quantityContainer: {
    display: "flex",
  },
  bottomBtnContainer: {
    justifyContent: "center",
    gap: 2,
    paddingInline: "16px",
    paddingBottom: 3,
    boxShadow: "0px -2px 10.6px 0px rgba(0, 0, 0, 0.20)",
    marginTop: 3,
  },
  bottomBtn: {
    margin: 0,
    width: "100%",
    padding: "10px 30px",
    display: "flex",
    gap: "8px",
    color: "#FFFFFF",
    fontSize: 17,
  },
};
