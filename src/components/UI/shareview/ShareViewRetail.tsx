import { useConfigApp } from "@/hooks/useConfigApp";
import { Box, Stack } from "@mui/material";
import React from "react";
import { openShareSheet } from "zmp-sdk/apis";
import { COLORS } from "../../../constants/themes";
import ShareOutLineIcon from "../../icon/ShareOutLineIcon";
import NttShareIcon from "@/components/icon/NttShareIcon";
import { copy } from "@/utils/common";
import { Platform } from "@/config";

interface IShareView {
  title: string;
  description: string;
  thumbnail: string;
  path?: string;
  overrideStyles?: React.CSSProperties;
}

export default function ShareViewV2({
  title,
  description,
  thumbnail,
  path,
  overrideStyles,
}: IShareView) {
  const appConfig = useConfigApp();
  const onShareCurrentPage = async () => {
    try {
      if (Platform === "zalo") {
        let sharedObj = {
          title,
          description,
          thumbnail,
        };
        if (path) {
          sharedObj = Object.assign(sharedObj, { path });
        }
        const data = await openShareSheet({
          type: "zmp_deep_link",
          data: sharedObj,
        });
      } else {
        handleCopyLink(`https://zalo.me/s/${import.meta.env.VITE_ZMP_APP_ID}` + path);
      }
    } catch (err) {
      console.log("onShareCurrentPage error", err);
    }
  };
  const handleCopyLink = (path) => {
    copy(path, "link");
  };

  return (
    <Stack
      bgcolor="#e5e5e5"
      p={1}
      sx={{ borderRadius: "0 8px 8px 0" }}
      onClick={onShareCurrentPage}
      mb={2}
    >
      <NttShareIcon fillColor={appConfig.color.primary} />
    </Stack>
  );
}

const styles: Record<string, React.CSSProperties> = {
  shareContainer: {
    // width: "45px",
  },
  shareContent: {
    display: "flex",
    alignItems: "center",
    width: "100%",
  },
  shareText: {
    fontSize: 16,
    paddingLeft: 8,
    fontWeight: 700,
    color: COLORS.primary1,
  },
};
