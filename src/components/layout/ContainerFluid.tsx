import { Box, Container, Grid, Stack } from "@mui/material";
import React, { ReactNode, useState } from "react";
import { Header } from "zmp-ui";
import styles from "@/css/styles.module.css";
import { COLORS } from "@/constants/themes";
import { useConfigApp } from "@/hooks/useConfigApp";
import ArrowLeftIcon from "../icon/ArrowLeftIcon";
import { Platform } from "@/config";
import { useNavigate } from "react-router-dom";
import { Router } from "@/constants/Route";

export default function FrameContainerFull({
  children,
  title,
  overrideStyle,
  rightComponent,
  onBackClick,
}: {
  children: ReactNode;
  title: string;
  overrideStyle?: React.CSSProperties;
  rightComponent?: ReactNode;
  onBackClick?: () => void;
}) {
  const navigate = useNavigate();
  const { color, bgColor, header } = useConfigApp();
  const [positionCss, setPositionCss] = useState({});
  const appConfig = useConfigApp();
  const handleScroll = (e) => {
    const bottom = e.target.scrollHeight - e.target.scrollTop === e.target.clientHeight;
    if (bottom) {
      // setPositionCss({
      //     position: 'fixed',
      // })
    }
  };
  return (
    <div style={{ backgroundColor: appConfig.container?.backgroundColor, minHeight: "100vh" }}>
      <Header
        backgroundColor={appConfig.header?.backgroundColor || "#FFFFFF"}
        textColor={COLORS.black}
        style={{ ...positionCss, fontWeight: 700 }}
        title={title}
        backIcon={<ArrowLeftIcon fillColor={COLORS.black} />}
        onBackClick={onBackClick ?? onBackClick}
      />
      <Box
        position={"fixed"}
        top={Platform === "zalo" ? 70 : 17}
        right={{
          xs: "16px",
          sm: "calc((100vw - 450px) / 2 + 22px)",
        }}
        zIndex={1000}
        sx={{
          // Ensure it doesn't overflow on small screens
          maxWidth: { xs: "calc(100vw - 120px)", sm: "auto" },
        }}
      >
        {rightComponent}
      </Box>
      <div className={styles.pageContent}>
        <Container
          onScroll={handleScroll}
          style={{ paddingInline: 0, backgroundColor: appConfig.container?.backgroundColor }}
        >
          {children}
        </Container>
      </div>
    </div>
  );
}
