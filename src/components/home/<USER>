import { useConfigApp } from "@/hooks/useConfigApp";
import { setSearchCondition } from "@/redux/slices/product/productListSlice";
import { RootState } from "@/redux/store";
import SearchIcon from "@mui/icons-material/Search";
import { Box, Container, Stack, useTheme } from "@mui/material";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useDebouncedCallback } from "use-debounce";
import LazyImage from "../UI/LazyImage";

export default function HeaderBar() {
  const theme = useTheme();
  const appConfig = useConfigApp();
  const { isLoading } = useSelector((state: RootState) => state.appInfo);

  const headerStyleItem = appConfig.header as any;
  const positionLogo = headerStyleItem?.alignLogo || "center";

  const validAlignItems = [
    "start",
    "center",
    "end",
    "flex-start",
    "flex-end",
    "baseline",
    "stretch",
  ];
  const alignItems = validAlignItems.includes(positionLogo) ? positionLogo : "start";
  const { user } = useSelector((state: RootState) => state.auth);
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const [searchValue, setSearchValue] = React.useState("");
  const [showClearIcon, setShowClearIcon] = React.useState("none");
  const [searchKey, setSearchKey] = React.useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const debounced = useDebouncedCallback((value) => {
    dispatch(setSearchCondition({ search: value, shopId: shopId || undefined }));
    navigate("/menu", { state: { searchKey: value } });
  }, 2000);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setShowClearIcon(event.target.value === "" ? "none" : "flex");
    setSearchKey(event.target.value);
    debounced(event.target.value);
  };
  const handleClick = () => {
    setSearchKey("");
    setShowClearIcon("none");
    debounced("");
  };

  return (
    <Container sx={{ bgcolor: "#ffffff00", py: 2 }} maxWidth={false}>
      <Stack direction="row" alignItems="center" spacing={4} sx={{ justifyContent: "center" }}>
        {/* Logo */}
        <Stack
          sx={{
            display: "flex",
            justifyContent: "center",
            ...styles.headerTitle,
            color: appConfig?.color?.primary || "#000",
            alignItems,
            bgcolor: "transparent",
            borderRadius: "50%",
            width: 56,
            height: 56,
          }}
        >
          {!isLoading && (
            <LazyImage
              src={
                typeof appConfig.shopLogo === "string" && appConfig.shopLogo
                  ? appConfig.shopLogo
                  : appConfig.shopLogo?.link || "/images/logo.png"
              }
              alt="logo"
              style={{
                objectFit: "cover",
              }}
              aspectRatio="1"
            />
          )}
        </Stack>

        {/* Search Bar */}
        <Box sx={{ flexGrow: 1, display: "flex", justifyContent: "center" }}>
          {appConfig.header?.search?.status && (
            <Box
              sx={{
                position: "relative",
                xs: "100%",
                sm: 300,
              }}
            >
              <SearchIcon
                sx={{
                  position: "absolute",
                  left: 18,
                  top: "50%",
                  transform: "translateY(-50%)",
                  color: "#d3d3d3",
                  fontSize: 24,
                }}
              />
              <input
                type="text"
                placeholder={appConfig.header?.search?.text || "Tìm kiếm sản phẩm"}
                value={searchKey}
                onChange={handleChange}
                style={{
                  width: "75%",
                  height: 36,
                  borderRadius: 28,
                  border: "2px solid #e2e2e2",
                  padding: "0 20px 0 50px",
                  fontSize: "14px",
                  background: "#fff",
                  outline: "none",
                  color: "#666",
                  fontWeight: 400,
                  boxSizing: "border-box",
                  transition: "border .2s",
                }}
                onFocus={(e) => (e.target.style.border = "2px solid #bcbcbc")}
                onBlur={(e) => (e.target.style.border = "2px solid #e2e2e2")}
              />
            </Box>
          )}
        </Box>
      </Stack>
    </Container>
  );
}

const styles: Record<string, React.CSSProperties> = {
  shopName: {
    display: "block",
    whiteSpace: "normal",
    lineHeight: 1.3,
  },
  headerLogo: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "transparent",
    borderRadius: "50%",
    width: 40,
    height: 40,
  },
  headerTitle: {
    width: "100%",
    // alignItems: "start",
  },
};
