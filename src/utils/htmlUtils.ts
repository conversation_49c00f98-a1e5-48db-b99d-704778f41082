/**
 * Utility functions for HTML processing
 */

/**
 * Strips HTML tags from a string and returns plain text
 * @param html - The HTML string to process
 * @returns Plain text with HTML tags removed
 */
export const stripHtmlTags = (html: string): string => {
  if (!html) return "";
  
  return html
    .replace(/<li[^>]*>/gi, "\n")
    .replace(/<br\s*\/?\s*>/gi, "\n")
    .replace(/<p[^>]*>/gi, "\n")
    .replace(/<\/li>|<\/p>/gi, "")
    .replace(/<[^>]+>/g, "")
    .replace(/\n+/g, "\n")
    .trim();
};

/**
 * Strips HTML tags and truncates text to a specified length
 * @param html - The HTML string to process
 * @param maxLength - Maximum length of the returned text
 * @returns Plain text with HTML tags removed and truncated
 */
export const stripHtmlAndTruncate = (html: string, maxLength: number = 100): string => {
  const plainText = stripHtmlTags(html);
  return plainText.length > maxLength ? plainText.slice(0, maxLength) : plainText;
};
