import { IProduct } from "./product";
import { ITaxInvoiceConfiguration } from "./taxInvoice";
import { VoucherDto } from "@/redux/slices/voucher/voucherSlice";

export interface ICart {
  cartId: string;
  cartNo: string;
  transactionId: string;
  partnerId: string;
  userId: string;
  addressId: string;
  shopId: string;
  listItems: IProduct[];
  voucherPromotion: VoucherDto[];
  voucherTransport: VoucherDto[];
  originPrice: number;
  savingMoney: number;
  price: number;
  exchangePoints: number;
  pointPrice: number;
  voucherPromotionPrice: number;
  voucherTransportPrice: number;
  transportPrice: number;
  transportService: string;
  statusDelivery?: string;
  typePay?: string;
  created: string;
  updated: string;
  branchId?: string;
  taxInvoice?: ITaxInvoiceConfiguration | null;
  totalTaxAmount?: number;
  totalAfterTax?: number;
  voucherCodes?: string[];
  notes?: string;
  cartOrigin?: string;
}
