import { Box, Stack, Typography } from "@mui/material";
import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";
import LazyImage from "@/components/UI/LazyImage";
import ShareViewV2 from "@/components/UI/shareview/ShareViewRetail";
import { AppEnv } from "@/config";
import { useConfigApp } from "@/hooks/useConfigApp";
import dayjs from "dayjs";
import DOMPurify from "dompurify";
import { useSelector } from "react-redux";
import { useNavigate, useParams } from "react-router-dom";
import FrameContainerFull from "../../components/layout/ContainerFluid";
import ListNews from "../../components/news/ListNews";
import { Router } from "../../constants/Route";
import { commonStyle } from "../../constants/themes";
import { getListNewsUser, getNewsList } from "../../redux/slices/news/newsListSlice";
import store, { RootState } from "../../redux/store";
import { Icon } from "@/constants/Assets";
import { stripHtmlAndTruncate } from "@/utils/htmlUtils";
import ShareView from "@/components/UI/shareview";

function fixContentWidth(html) {
  if (!html) return "";
  html = html.replace(/width\s*:\s*[^;"]+;?/gi, "width:100%!important;");
  html = html.replace(/width\s*=\s*["'][^"']+["']/gi, 'width="100%"');
  return html;
}

export default function PostDetail() {
  const appConfig = useConfigApp();
  const navigate = useNavigate();
  const { newsList, userNewsList, bannerWithBranchShopConfig, bannerHomeShopConfig } = useSelector(
    (state: RootState) => state.newsList
  );
  const { shopId } = useSelector((state: RootState) => state.appInfo);
  const { user } = useSelector((state: RootState) => state.auth);
  const { id } = useParams();
  const item = [
    ...newsList,
    ...bannerWithBranchShopConfig,
    ...bannerHomeShopConfig,
    ...userNewsList,
  ].find((n) => n.articleId === id);
  const location = useLocation();
  const news = location.state?.news ?? item;
  const pageTitle = location.state?.title || "Tin tức";

  useEffect(() => {
    if (shopId) {
      store.dispatch(getNewsList());
      store.dispatch(getListNewsUser(shopId));
    }
  }, [shopId]);

  return (
    <FrameContainerFull title={pageTitle} overrideStyle={styles.frameContainerStyle}>
      <Stack>
        <LazyImage
          src={
            Array.isArray(news?.images) && news?.images.length > 0 && news?.images[0]?.link
              ? news?.images[0].link
              : appConfig?.shopLogo?.link || appConfig?.shopLogo || Icon.errorImage
          }
          alt={news?.title}
          style={styles.imageStyle}
          aspectRatio="16/9"
        />
      </Stack>
      <Box style={styles.titleContainer}>
        <Box sx={{ maxWidth: "92%" }}>
          <Typography
            style={{
              ...styles.titleStyle,
              overflow: "hidden",
              textOverflow: "ellipsis",
            }}
          >
            {news?.title}
          </Typography>
          <Typography style={styles.timeStyle}>
            {dayjs(news?.created).format("HH:mm, DD/MM")}
          </Typography>
        </Box>
        <ShareView
          title={news?.title}
          description={stripHtmlAndTruncate(news?.content || "", 100)}
          thumbnail={
            Array.isArray(news?.images) && news?.images.length > 0 && news?.images[0]?.link
              ? news?.images[0].link
              : appConfig?.shopLogo?.link || appConfig?.shopLogo || Icon.errorImage
          }
          path={
            AppEnv === "dev"
              ? `?env=TESTING&page=posts&id=${id}&refCode=${user?.referralCode}`
              : `?page=posts&id=${id}&refCode=${user?.referralCode}`
          }
          overrideStyles={{ marginBlock: 0.7 }}
        />

        {/* <ShareOutLineIcon fillColor={color.primary} /> */}
      </Box>
      <Stack style={styles.contentContainer}>
        <Typography style={{ ...styles.titleStyle, marginBottom: 10 }}>
          Nội dung chi tiết
        </Typography>
        <Box
          className="post-content"
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(fixContentWidth(news?.content)),
          }}
          style={styles.contentBoxStyle}
        />
        <style>{`
          .post-content, .post-content * {
            word-break: break-word !important;
          }
          .post-content img {
            max-width: 100% !important;
            height: auto !important;
            display: block;
            margin: 0 auto;
          }
        `}</style>
      </Stack>
      <Stack style={styles.relatedNewsContainer}>
        <Box style={styles.relatedNewsHeader}>
          <Typography style={commonStyle.headline18}>Tin tức liên quan</Typography>
          <Typography
            onClick={() => {
              navigate(Router.post.index);
            }}
            style={styles.seeMoreStyle}
          >
            Xem thêm
          </Typography>
        </Box>
        <ListNews
          list={newsList.filter((item) => item.articleId !== news?.articleId)}
          titleFromParent={pageTitle}
        />
      </Stack>
    </FrameContainerFull>
  );
}

const styles: Record<string, React.CSSProperties> = {
  frameContainerStyle: {
    background: "#e9ebed",
  },
  imageStyle: {
    // aspectRatio: "7 / 4",
    objectFit: "cover",
  },
  titleContainer: {
    background: "#FFF",
    padding: 16,
    alignItems: "end",
    justifyContent: "space-between",
    display: "flex",
    marginBottom: "8px",
  },
  titleStyle: {
    fontSize: "17px",
    color: "#1D1D1D",
    fontWeight: 700,
    letterSpacing: "0.17px",
  },
  timeStyle: {
    fontWeight: 400,
    fontSize: 12,
    color: "#B5B5B5",
    marginTop: "5px",
  },
  contentContainer: {
    background: "#FFFFFF",
    padding: 16,
    marginBottom: "8px",
  },
  contentBoxStyle: {
    overflowX: "hidden",
    maxWidth: "100%",
  },
  relatedNewsContainer: {
    padding: 16,
    marginBlock: 1,
    background: "#fff",
  },
  relatedNewsHeader: {
    marginTop: 2,
    marginBottom: 1,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  seeMoreStyle: {
    color: "#878787",
    fontSize: "13px",
    fontWeight: 500,
  },
};
