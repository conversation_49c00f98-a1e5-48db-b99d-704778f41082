import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { Payment } from "zmp-sdk";
import { AppEnv, Platform } from "../config";
import { StatusDelivery, TransportService, TypeOrigin, TypePay } from "../constants/Const";
import { getCart, setCart, setCartPayment } from "../redux/slices/cart/cartSlice";
import { createMac, createOrderOnServer } from "../redux/slices/order/orderSlice";
import { AppDispatch, RootState } from "../redux/store";
import { ICart } from "../types/cart";
import { showToast } from "../utils/common";
import { useConfigApp } from "./useConfigApp";

const ship = 0;

export const useCart = () => {
  const { cartPayment, availableDiscountPoint, pointAfterOrderComplete } = useSelector(
    (state: RootState) => state.cart
  );

  const { shopInfo } = useSelector((state: RootState) => state.appInfo);

  const { user } = useSelector((state: RootState) => state.auth);
  const { currentAddress } = useSelector((state: RootState) => state.address);
  const { defaultBranch } = useSelector((state: RootState) => state.branch);
  const { listPaymentMethod } = useSelector((state: RootState) => state.paymentMethod);
  const appConfig = useConfigApp();
  const dispatch = useDispatch<AppDispatch>();

  const createOrderZalo = async (body) => {
    const item = body.listItems.map((item) => ({
      id: String(item.itemsId),
      amount: item.quantity * item.price,
    }));

    let idPaymentMethod = body.typePay.toUpperCase();
    if (body.typePay === "Transfer") {
      idPaymentMethod = "BANK";
    }

    const paymentMethod = {
      id: AppEnv === "production" ? idPaymentMethod : idPaymentMethod + "_SANDBOX",
      isCustom: false,
    };

    const extraData = {
      storeName: "Kho tổng",
      storeId: "1",
      orderId: body.orderId,
      notes: body.notes,
    };

    const orderData: any = {
      desc: `Thanh toán ${body.orderNo}`,
      item,
      amount: body.totalAfterTax,
      extradata: JSON.stringify(extraData),
      method: JSON.stringify(paymentMethod),
    };

    const mac = await dispatch(createMac(orderData)).unwrap();

    orderData.mac = mac;

    // dispatch(trackingPayment(orderData));

    return new Promise((resolve, reject) => {
      Payment.createOrder({
        ...orderData,
        success: resolve,
        fail: reject,
      });
    });
  };

  const createOrder = async () => {
    try {
      const res: any = await dispatch(createOrderOnServer()).unwrap();
      if (res?.orderId) {
        if (Platform === "zalo" && res.typePay !== TypePay.TRANSFER) {
          const resZalo = await createOrderZalo(res);
        }
      }
      return res;
    } catch (e: any) {
      return {
        error: true,
        message: e || "Có lỗi xảy ra khi tạo đơn hàng",
      };
    }
  };

  const setCartAndSave = async (newCart: ICart) => {
    const updatedCart = Object.assign({}, newCart, {
      addressId: newCart.addressId || currentAddress?.shippingAddressId || "",
    });
    updatedCart.transportService = TransportService.LCOD;
    updatedCart.branchId = defaultBranch?.branchId;

    if (!updatedCart.cartOrigin) {
      updatedCart.cartOrigin = Platform === "zalo" ? TypeOrigin.ZaloMiniApp : TypeOrigin.WebApp;
    }

    if (!shopInfo?.enableExpressDelivery && updatedCart.statusDelivery == StatusDelivery.InHome) {
      updatedCart.statusDelivery = StatusDelivery.InShop;
    }
    if (!shopInfo?.enableInShop && updatedCart.statusDelivery == StatusDelivery.InShop) {
      updatedCart.statusDelivery = StatusDelivery.InHome;
    }
    if (!updatedCart.typePay) {
      if (
        Array.isArray(listPaymentMethod) &&
        listPaymentMethod.length > 0 &&
        listPaymentMethod[0]?.typePay
      ) {
        updatedCart.typePay = listPaymentMethod[0].typePay;
      }
    }
    try {
      if (user) {
        await dispatch(setCart(updatedCart)).unwrap();
        // Đợi lấy dữ liệu cart mới nhất sau khi cập nhật
        await dispatch(getCart());
        return true;
      }
      updatedCart.userId = "";
      updatedCart.cartId = "";
      updatedCart.transportPrice =
        updatedCart.statusDelivery == StatusDelivery.InShop ? 0 : appConfig?.transportPrice || 0;
      updatedCart.price =
        updatedCart.listItems.reduce((sum, item) => sum + (item.price || 0) * item.quantity, 0) +
        updatedCart.transportPrice;
      dispatch(setCartPayment(updatedCart));
      return true;
      // setItem(StorageKeys.Cart, newCart);
    } catch (error) {
      await dispatch(getCart());
      throw error;
    }
  };

  const addProduct = (product, quantity = 1) => {
    const existingItemIndex = cartPayment?.listItems?.findIndex(
      (item) =>
        _.isEqual(new Set(item.extraOptions), new Set(product.extraOptions)) &&
        item.note === product.note &&
        item.itemsCode === product.itemsCode &&
        item.variantNameOne === product.variantNameOne &&
        item.variantValueOne === product.variantValueOne &&
        item.variantNameTwo === product.variantNameTwo &&
        item.variantValueTwo === product.variantValueTwo &&
        item.variantNameThree === product.variantNameThree &&
        item.variantValueThree === product.variantValueThree
    );
    if (existingItemIndex !== undefined && existingItemIndex >= 0) {
      return cartPayment?.listItems.map((item, index) =>
        index === existingItemIndex ? { ...item, quantity: (item.quantity || 0) + quantity } : item
      );
    } else {
      return [...(cartPayment?.listItems || []), { ...product, quantity }];
    }
  };

  const recalculateCart = (items) => {
    const totalPrice = items.reduce((total, item) => {
      const price = item.product.price;
      return total + item.quantity * price;
    }, 0);

    // const totalDiscount = items.reduce((total, item) => {
    //   const discount =
    //     item.product.attributes.selectVariant?.discount ||
    //     item.product.attributes.discount;
    //   return total + item.quantity * discount;
    // }, 0);
    const totalDiscount = 0;
    const finalPrice = totalPrice - totalDiscount + ship;

    return {
      totalPrice,
      totalDiscount,
      ship,
      finalPrice,
      payPrice: finalPrice,
    };
  };

  const addProductToCart = async (product, quantity = 0) => {
    try {
      const listItems = addProduct(product, quantity) || [];
      const response = await setCartAndSave({
        ...cartPayment,
        listItems,
        // ...recalculateCart(items),
      });
      return response;
    } catch (error) {
      console.log({ error });
      await setCartAndSave({
        ...cartPayment,
        voucherPromotion: [],
        voucherTransport: [],
        // ...recalculateCart(items),
      });
      return false;
    }
  };

  const removeProductFromCart = async (product) => {
    const listItems =
      cartPayment?.listItems?.filter((item) => {
        return !(
          _.isEqual(new Set(item.extraOptions), new Set(product.extraOptions)) &&
          item.itemsCode === product.itemsCode &&
          item.note === product.note &&
          item.variantNameOne === product.variantNameOne &&
          item.variantValueOne === product.variantValueOne &&
          item.variantNameTwo === product.variantNameTwo &&
          item.variantValueTwo === product.variantValueTwo &&
          item.variantNameThree === product.variantNameThree &&
          item.variantValueThree === product.variantValueThree
        );
      }) || [];
    try {
      if (listItems.length === 0) {
        await setCartAndSave({
          ...cartPayment,
          listItems,
          voucherPromotion: [],
          voucherTransport: [],
        });
      } else {
        await setCartAndSave({
          ...cartPayment,
          listItems,
        });
      }
    } catch (error: any) {
      if (error.status === 400) {
        await dispatch(
          setCartPayment({
            ...cartPayment,
            listItems: listItems,
            voucherPromotion: [],
            voucherTransport: [],
          })
        );
        await setCartAndSave({
          ...cartPayment,
          listItems,
          voucherPromotion: [],
          voucherTransport: [],
        });
      }
      return error;
    }
  };

  const changeProductQuantity = async (product, quantity: number, note: string) => {
    try {
      const listItems =
        cartPayment?.listItems?.map((item) => {
          return item.itemsCode === product.itemsCode &&
            _.isEqual(new Set(item.extraOptions), new Set(product.extraOptions)) &&
            item.note === product.note &&
            item.variantNameOne === product.variantNameOne &&
            item.variantValueOne === product.variantValueOne &&
            item.variantNameTwo === product.variantNameTwo &&
            item.variantValueTwo === product.variantValueTwo &&
            item.variantNameThree === product.variantNameThree &&
            item.variantValueThree === product.variantValueThree
            ? { ...item, quantity, note }
            : item;
        }) || [];

      const response = await setCartAndSave({
        ...cartPayment,
        listItems,
      });
      return response;
    } catch (error) {
      return error;
    }
  };

  const clearCart = () => {
    setCartAndSave({
      ...cartPayment,
      listItems: [],
    });
  };

  const changeDelivery = (statusDelivery) => {
    setCartAndSave({
      ...cartPayment,
      statusDelivery,
    });
  };

  const changeAddress = (addressId, statusDelivery) => {
    setCartAndSave({
      ...cartPayment,
      statusDelivery,
      addressId,
    });
  };

  const changeBranch = (branchId, statusDelivery) => {
    setCartAndSave({
      ...cartPayment,
      statusDelivery,
      branchId,
    });
  };

  const changePaymentMethod = (typePay) => {
    setCartAndSave({
      ...cartPayment,
      typePay,
    });
  };

  const toggleUsePoint = (pointValue) => {
    setCartAndSave({
      ...cartPayment,
      exchangePoints: pointValue,
    });
  };

  return {
    setCartAndSave,
    cartPayment,
    addProductToCart,
    removeProductFromCart,
    changeProductQuantity,
    clearCart,
    createOrder,
    changeDelivery,
    changeAddress,
    changePaymentMethod,
    changeBranch,
    toggleUsePoint,
    createOrderZalo,
    availableDiscountPoint,
    pointAfterOrderComplete,
  };
};
