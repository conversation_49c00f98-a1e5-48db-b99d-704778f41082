import { Platform } from "@/config";
import { StatusDelivery, TransportService, TypeOrigin } from "@/constants/Const";
import { request } from "@/utils/request";
import { createAsyncThunk, createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ICart } from "../../../types/cart";

interface CartState {
  // cart: ICart;
  cartPayment: ICart;
  isLoading: boolean;
  availableDiscountPoint: number;
  pointAfterOrderComplete: number;
}

const initialState: CartState = {
  // cart: {
  //   cartId: "",
  //   cartNo: "",
  //   transactionId: "",
  //   partnerId: "",
  //   userId: "",
  //   addressId: "",
  //   shopId: "",
  //   listItems: [],
  //   voucherPromotion: [],
  //   voucherTransport: [],
  //   price: 0,
  //   exchangePoints: 0,
  //   pointPrice: 0,
  //   voucherPromotionPrice: 0,
  //   voucherTransportPrice: 0,
  //   transportPrice: 0,
  //   transportService: TransportService.LCOD,
  //   statusDelivery: StatusDelivery.InShop,
  //   typePay: TypePay.COD,
  //   created: "",
  //   updated: "",
  // },
  cartPayment: {
    cartId: "",
    cartNo: "",
    transactionId: "",
    partnerId: "",
    userId: "",
    addressId: "",
    shopId: "",
    listItems: [],
    voucherPromotion: [],
    voucherTransport: [],
    price: 0,
    exchangePoints: 0,
    pointPrice: 0,
    voucherPromotionPrice: 0,
    voucherTransportPrice: 0,
    transportPrice: 0,
    transportService: TransportService.LCOD,
    statusDelivery: StatusDelivery.InHome,
    // typePay: null,
    created: "",
    updated: "",
    branchId: "",
    originPrice: 0,
    savingMoney: 0,
    cartOrigin: Platform === "zalo" ? TypeOrigin.ZaloMiniApp : TypeOrigin.WebApp,
  },
  isLoading: true,
  availableDiscountPoint: 0,
  pointAfterOrderComplete: 0,
};

export const getCart = createAsyncThunk("cart/getCart", async () => {
  const response: any = await request("get", "/api/user/cartuser/getcart");
  return response;
});

export const getCartInfo = createAsyncThunk("cart/getCartInfo", async () => {
  const response: any = await request("get", "/api/user/cartuser/getcart");
  return response;
});

export const setCart = createAsyncThunk(
  "cart/setCart",
  async (cart: ICart, { rejectWithValue }) => {
    try {
      const patchedCart = {
        ...cart,
        taxInvoice:
          Array.isArray(cart.listItems) && cart.listItems.length === 0 ? null : cart.taxInvoice,
      };
      const response: any = await request(
        "post",
        "/api/user/cartuser/createorupdatecart",
        patchedCart
      );
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const estimatePointCart = createAsyncThunk("cart/estimatePointCart", async () => {
  try {
    const response: any = await request("get", "/api/user/cartuser/estimatePointCart");
    return response;
  } catch (error) {
    return error;
  }
});

const cartSlice = createSlice({
  name: "cart",
  initialState,
  reducers: {
    // setCart: (state, action: PayloadAction<ICart | null>) => {
    //   state.cart = action?.payload;
    //   state.cartPayment =
    //     action?.payload?.items.map((i) => i.product.id) || [];
    // },
    setCartPayment: (state, action: PayloadAction<ICart>) => {
      state.cartPayment = action?.payload;
    },
    updateCartStatusDelivery: (state, action: PayloadAction<string>) => {
      // state.cart.statusDelivery = action.payload;
      state.cartPayment.statusDelivery = action.payload;
    },
    updatePriceCartPayment: (state, action: PayloadAction<number>) => {
      state.cartPayment.price = action.payload;
    },
    updateVoucherPromotionCartPayment: (state, action: PayloadAction<any>) => {
      state.cartPayment.voucherPromotion = action.payload;
    },
    updateVoucherTransportCartPayment: (state, action: PayloadAction<any>) => {
      state.cartPayment.voucherTransport = action.payload;
    },
    updateAddressCartPayment: (state, action: PayloadAction<string>) => {
      state.cartPayment.addressId = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCart.fulfilled, (state, action: PayloadAction<ICart>) => {
        const { payload } = action;
        // state.cart = payload;
        state.cartPayment = payload;
        state.isLoading = false;
      })
      .addCase(getCart.rejected, (state, action) => {
        state.isLoading = false;
      })
      .addCase(estimatePointCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(estimatePointCart.fulfilled, (state, action) => {
        const { payload } = action;
        state.availableDiscountPoint = payload.maxExchangePoint;
        state.pointAfterOrderComplete = payload.pointAfterCompleteOrder;
        state.isLoading = false;
      })
      .addCase(estimatePointCart.rejected, (state, action) => {
        state.isLoading = false;
      });
  },
});

export const {
  setCartPayment,
  updateCartStatusDelivery,
  updatePriceCartPayment,
  updateVoucherPromotionCartPayment,
  updateVoucherTransportCartPayment,
  updateAddressCartPayment,
} = cartSlice.actions;

export default cartSlice.reducer;
